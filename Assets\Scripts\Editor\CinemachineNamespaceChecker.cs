/*
 * Boder Game Development
 * File: CinemachineNamespaceChecker.cs
 * Author: Lead Programmer
 * Created: [Current Date]
 * Task: P1-002 (Fix)
 * Description: Check correct Cinemachine namespace for Unity 6
 */

using UnityEngine;
using UnityEditor;
using System;
using System.Reflection;

namespace Boder.Editor
{
    /// <summary>
    /// Tool to check correct Cinemachine namespace and types in Unity 6
    /// </summary>
    public class CinemachineNamespaceChecker : EditorWindow
    {
        [MenuItem("Boder/Check Cinemachine Namespace")]
        public static void CheckCinemachineNamespace()
        {
            Debug.Log("[P1-002] Checking Cinemachine namespace and types...");
            
            // Try to find Cinemachine assemblies
            var assemblies = AppDomain.CurrentDomain.GetAssemblies();
            
            foreach (var assembly in assemblies)
            {
                if (assembly.FullName.Contains("Cinemachine"))
                {
                    Debug.Log($"[P1-002] Found Cinemachine assembly: {assembly.FullName}");
                    
                    // Check for common Cinemachine types
                    CheckTypeInAssembly(assembly, "CinemachineVirtualCamera");
                    CheckTypeInAssembly(assembly, "CinemachineFreeLook");
                    CheckTypeInAssembly(assembly, "CinemachinePOV");
                    CheckTypeInAssembly(assembly, "CinemachineVirtualCameraBase");
                }
            }
            
            // Try specific namespace checks
            CheckSpecificNamespaces();
            
            Debug.Log("[P1-002] Cinemachine namespace check completed.");
        }
        
        private static void CheckTypeInAssembly(Assembly assembly, string typeName)
        {
            try
            {
                var types = assembly.GetTypes();
                foreach (var type in types)
                {
                    if (type.Name == typeName)
                    {
                        Debug.Log($"[P1-002] ✅ Found {typeName} in namespace: {type.Namespace}");
                        return;
                    }
                }
                Debug.LogWarning($"[P1-002] ❌ {typeName} not found in assembly {assembly.GetName().Name}");
            }
            catch (Exception e)
            {
                Debug.LogError($"[P1-002] Error checking types in assembly {assembly.GetName().Name}: {e.Message}");
            }
        }
        
        private static void CheckSpecificNamespaces()
        {
            // Check common Cinemachine namespaces
            string[] possibleNamespaces = {
                "Cinemachine",
                "Unity.Cinemachine", 
                "UnityEngine.Cinemachine",
                "Cinemachine.Core"
            };
            
            foreach (string namespaceName in possibleNamespaces)
            {
                Debug.Log($"[P1-002] Checking namespace: {namespaceName}");
                
                // Try to find types in this namespace
                if (TryFindType($"{namespaceName}.CinemachineVirtualCamera"))
                {
                    Debug.Log($"[P1-002] ✅ CinemachineVirtualCamera found in {namespaceName}");
                }
                
                if (TryFindType($"{namespaceName}.CinemachineFreeLook"))
                {
                    Debug.Log($"[P1-002] ✅ CinemachineFreeLook found in {namespaceName}");
                }
                
                if (TryFindType($"{namespaceName}.CinemachinePOV"))
                {
                    Debug.Log($"[P1-002] ✅ CinemachinePOV found in {namespaceName}");
                }
            }
        }
        
        private static bool TryFindType(string fullTypeName)
        {
            try
            {
                Type type = Type.GetType(fullTypeName);
                if (type != null)
                {
                    return true;
                }
                
                // Try in all loaded assemblies
                foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
                {
                    type = assembly.GetType(fullTypeName);
                    if (type != null)
                    {
                        return true;
                    }
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
        
        [MenuItem("Boder/Generate Cinemachine Fix")]
        public static void GenerateCinemachineFix()
        {
            Debug.Log("[P1-002] Generating Cinemachine namespace fix...");
            
            // Check if we can access Cinemachine types with Unity.Cinemachine namespace
            if (TryFindType("Unity.Cinemachine.CinemachineVirtualCamera"))
            {
                Debug.Log("[P1-002] ✅ Unity.Cinemachine namespace works!");
                CreateNamespaceFix("Unity.Cinemachine");
            }
            else if (TryFindType("Cinemachine.CinemachineVirtualCamera"))
            {
                Debug.Log("[P1-002] ✅ Cinemachine namespace works!");
                CreateNamespaceFix("Cinemachine");
            }
            else
            {
                Debug.LogError("[P1-002] ❌ Cannot find working Cinemachine namespace!");
                EditorUtility.DisplayDialog("Cinemachine Not Found", 
                    "Cannot find Cinemachine types in any namespace.\n\nPlease ensure Cinemachine package is properly installed.", "OK");
                return;
            }
        }
        
        private static void CreateNamespaceFix(string correctNamespace)
        {
            string fixContent = $@"/*
 * Cinemachine Namespace Fix
 * Generated automatically for Unity 6 compatibility
 * Correct namespace: {correctNamespace}
 */

// If you're getting Cinemachine compilation errors, 
// replace 'using Cinemachine;' with 'using {correctNamespace};' 
// in the following files:

// Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Camera/CameraManager.cs
// Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Camera/FirstPersonCameraController.cs  
// Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Camera/ThirdPersonCameraController.cs

// Correct using statement:
// using {correctNamespace};
";

            System.IO.File.WriteAllText("Assets/CINEMACHINE_FIX_INSTRUCTIONS.txt", fixContent);
            AssetDatabase.Refresh();
            
            Debug.Log($"[P1-002] ✅ Fix instructions created! Correct namespace is: {correctNamespace}");
            
            EditorUtility.DisplayDialog("Fix Generated", 
                $"Cinemachine fix generated!\n\nCorrect namespace: {correctNamespace}\n\nSee CINEMACHINE_FIX_INSTRUCTIONS.txt for details.", "OK");
        }
    }
}
