/*
 * Boder Game Development
 * File: ForceRecompileTool.cs
 * Author: Lead Programmer
 * Created: [Current Date]
 * Task: P1-002 (Fix)
 * Description: Tool to force Unity recompilation and refresh
 */

using UnityEngine;
using UnityEditor;
using UnityEditor.Compilation;

namespace Boder.Editor
{
    /// <summary>
    /// Tool to force Unity recompilation and refresh to fix assembly issues
    /// </summary>
    public class ForceRecompileTool : EditorWindow
    {
        [MenuItem("Boder/Force Recompile & Refresh")]
        public static void ForceRecompileAndRefresh()
        {
            Debug.Log("[P1-002] Starting force recompile and refresh...");
            
            // Step 1: Refresh Asset Database
            Debug.Log("[P1-002] Refreshing Asset Database...");
            AssetDatabase.Refresh();
            
            // Step 2: Force recompile all assemblies
            Debug.Log("[P1-002] Requesting script compilation...");
            CompilationPipeline.RequestScriptCompilation();
            
            // Step 3: Clear console
            var logEntries = System.Type.GetType("UnityEditor.LogEntries, UnityEditor.dll");
            var clearMethod = logEntries.GetMethod("Clear", System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.Public);
            clearMethod?.Invoke(null, null);
            
            Debug.Log("[P1-002] Force recompile completed. Check for compilation errors.");
            
            EditorUtility.DisplayDialog("Force Recompile", 
                "Force recompile and refresh completed.\n\nPlease check the Console for any remaining compilation errors.", "OK");
        }

        [MenuItem("Boder/Reimport All Assets")]
        public static void ReimportAllAssets()
        {
            Debug.Log("[P1-002] Starting reimport all assets...");
            
            AssetDatabase.ImportAsset("Assets", ImportAssetOptions.ImportRecursive);
            
            Debug.Log("[P1-002] Reimport all assets completed.");
            
            EditorUtility.DisplayDialog("Reimport Complete", 
                "All assets have been reimported.\n\nThis may take a few minutes to complete.", "OK");
        }

        [MenuItem("Boder/Fix Assembly References")]
        public static void FixAssemblyReferences()
        {
            Debug.Log("[P1-002] Fixing assembly references...");
            
            // Check if Cinemachine is properly installed
            var cinemachinePackage = UnityEditor.PackageManager.PackageInfo.FindForAssetPath("Packages/com.unity.cinemachine");
            if (cinemachinePackage == null)
            {
                Debug.LogError("[P1-002] Cinemachine package not found! Please install it via Package Manager.");
                EditorUtility.DisplayDialog("Package Missing", 
                    "Cinemachine package is not installed.\n\nPlease install it via Window > Package Manager > Unity Registry > Cinemachine", "OK");
                return;
            }
            
            Debug.Log($"[P1-002] Cinemachine package found: version {cinemachinePackage.version}");
            
            // Force refresh assembly definitions
            var asmdefFiles = AssetDatabase.FindAssets("t:AssemblyDefinitionAsset");
            foreach (var guid in asmdefFiles)
            {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
                Debug.Log($"[P1-002] Reimported assembly definition: {path}");
            }
            
            // Request compilation
            CompilationPipeline.RequestScriptCompilation();
            
            Debug.Log("[P1-002] Assembly reference fix completed.");
            
            EditorUtility.DisplayDialog("Assembly Fix Complete", 
                "Assembly references have been refreshed.\n\nCompilation will restart automatically.", "OK");
        }

        [MenuItem("Boder/Validate Cinemachine Installation")]
        public static void ValidateCinemachineInstallation()
        {
            Debug.Log("[P1-002] Validating Cinemachine installation...");
            
            // Check package
            var cinemachinePackage = UnityEditor.PackageManager.PackageInfo.FindForAssetPath("Packages/com.unity.cinemachine");
            if (cinemachinePackage == null)
            {
                Debug.LogError("[P1-002] ❌ Cinemachine package not installed!");
                EditorUtility.DisplayDialog("Validation Failed", 
                    "Cinemachine package is not installed.\n\nInstall via: Window > Package Manager > Unity Registry > Cinemachine", "OK");
                return;
            }
            
            Debug.Log($"[P1-002] ✅ Cinemachine package installed: {cinemachinePackage.version}");
            
            // Check if assembly can be found
            var assemblies = CompilationPipeline.GetAssemblies();
            bool cinemachineAssemblyFound = false;
            
            foreach (var assembly in assemblies)
            {
                if (assembly.name.Contains("Cinemachine"))
                {
                    cinemachineAssemblyFound = true;
                    Debug.Log($"[P1-002] ✅ Found Cinemachine assembly: {assembly.name}");
                }
            }
            
            if (!cinemachineAssemblyFound)
            {
                Debug.LogWarning("[P1-002] ⚠️ Cinemachine assembly not found in compilation pipeline");
            }
            
            // Check assembly definition references
            var nappinAsmdef = AssetDatabase.LoadAssetAtPath<AssemblyDefinitionAsset>("Assets/Nappin/PhysicsCharacterController/Scripts/Nappin.PhysicsCharacterController.asmdef");
            if (nappinAsmdef != null)
            {
                Debug.Log("[P1-002] ✅ Nappin assembly definition found");
                var asmdefText = nappinAsmdef.text;
                if (asmdefText.Contains("Unity.Cinemachine"))
                {
                    Debug.Log("[P1-002] ✅ Cinemachine reference found in Nappin assembly definition");
                }
                else
                {
                    Debug.LogError("[P1-002] ❌ Cinemachine reference missing in Nappin assembly definition");
                }
            }
            else
            {
                Debug.LogError("[P1-002] ❌ Nappin assembly definition not found");
            }
            
            Debug.Log("[P1-002] Cinemachine validation completed. Check console for results.");
            
            string message = cinemachinePackage != null && cinemachineAssemblyFound ? 
                "Cinemachine validation passed!\n\nIf you still have compilation errors, try 'Force Recompile & Refresh'." :
                "Cinemachine validation found issues!\n\nCheck the Console for details.";
                
            EditorUtility.DisplayDialog("Validation Complete", message, "OK");
        }
    }
}
